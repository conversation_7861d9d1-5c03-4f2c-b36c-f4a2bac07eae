import pandas as pd
import os
from collections import defaultdict
import numpy as np

def read_excel_file(file_path):
    """读取Excel文件，处理不同的文件格式"""
    try:
        # 尝试读取Excel文件
        if file_path.endswith('.xlsx'):
            df = pd.read_excel(file_path, engine='openpyxl')
        else:
            df = pd.read_excel(file_path, engine='xlrd')
        return df
    except Exception as e:
        print(f"读取文件 {file_path} 时出错: {e}")
        return None

def analyze_monthly_data(df, month):
    """分析月度数据，统计各乡镇街道的省平台和主站数量"""
    if df is None:
        return {}

    print(f"\n=== {month}月数据分析 ===")
    print("数据框形状:", df.shape)
    print("列名:", df.columns.tolist())

    # 检查第一行是否是标题行
    if len(df) > 0:
        first_row = [str(x) for x in df.iloc[0].values]
        print("第一行数据:", first_row)

        # 如果第一行包含"乡镇街道"、"省平台"、"主站"，则使用第一行作为列名
        if '乡镇街道' in first_row and '省平台' in first_row and '主站' in first_row:
            # 重新设置列名
            df.columns = first_row
            df = df.iloc[1:].reset_index(drop=True)  # 删除第一行
            print("使用第一行作为列名，更新后的列名:", df.columns.tolist())

    # 现在查找列
    town_column = '乡镇街道' if '乡镇街道' in df.columns else df.columns[0]
    province_column = '省平台' if '省平台' in df.columns else (df.columns[1] if len(df.columns) > 1 else None)
    main_column = '主站' if '主站' in df.columns else (df.columns[2] if len(df.columns) > 2 else None)

    print(f"使用的列: 乡镇列={town_column}, 省平台列={province_column}, 主站列={main_column}")

    result = {}
    current_town = None

    for idx, row in df.iterrows():
        town_name = str(row[town_column]).strip()

        # 如果当前行有乡镇名称，更新当前乡镇
        if town_name != 'nan' and town_name != '':
            current_town = town_name
            if current_town not in result:
                result[current_town] = {'省平台': 0, '主站': 0}

        # 如果没有当前乡镇，跳过
        if current_town is None:
            continue

        # 统计省平台数量
        if province_column is not None:
            province_content = str(row[province_column])
            if province_content != 'nan' and province_content.strip() != '':
                result[current_town]['省平台'] += 1

        # 统计主站数量
        if main_column is not None:
            main_content = str(row[main_column])
            if main_content != 'nan' and main_content.strip() != '':
                result[current_town]['主站'] += 1

    # 打印结果
    for town, counts in result.items():
        print(f"{town}: 省平台={counts['省平台']}, 主站={counts['主站']}")

    return result

def main():
    # 自动获取文件列表
    all_files = [f for f in os.listdir('.') if f.endswith(('.xls', '.xlsx'))]
    files = []
    for month in range(1, 7):
        for file in all_files:
            if f'2025.{month}' in file and '汇总' not in file:
                files.append(file)
                break

    print(f"找到的月度文件: {files}")
    
    # 读取汇总文件获取乡镇街道列表
    summary_files = [f for f in all_files if '汇总' in f and '2024' in f and not f.startswith('~$')]
    summary_file = summary_files[0] if summary_files else None
    if summary_file:
        print(f"读取汇总文件: {summary_file}")
        summary_df = read_excel_file(summary_file)

        if summary_df is not None:
            print("汇总文件列名:", summary_df.columns.tolist())
            print("汇总文件前几行:")
            print(summary_df.head())
    else:
        print("未找到汇总文件")
        summary_df = None
    
    # 收集所有乡镇街道名称
    all_towns = set()
    monthly_data = {}
    
    # 处理每个月的数据
    for i, file in enumerate(files, 1):
        if os.path.exists(file):
            print(f"\n处理文件: {file}")
            df = read_excel_file(file)
            month_data = analyze_monthly_data(df, i)
            monthly_data[i] = month_data
            all_towns.update(month_data.keys())
        else:
            print(f"文件不存在: {file}")
    
    # 创建汇总表格
    print(f"\n发现的乡镇街道: {sorted(all_towns)}")

    # 指定的乡镇街道顺序
    town_order = [
        '太平街道',
        '城东街道',
        '城西街道',
        '城北街道',
        '横峰街道',
        '泽国镇',
        '大溪镇',
        '松门镇',
        '箬横镇',
        '新河镇',
        '石塘镇',
        '滨海镇',
        '温峤镇',
        '城南镇',
        '石桥头镇',
        '坞根镇'
    ]

    # 创建月度汇总表
    columns = ['乡镇街道']
    for month in range(1, 7):
        columns.extend([f'{month}月省平台', f'{month}月主站'])
    columns.extend(['总计省平台', '总计主站'])

    summary_data = []

    # 按指定顺序处理乡镇街道
    for town in town_order:
        if town not in all_towns:
            continue  # 如果该乡镇在数据中不存在，跳过
        row = [town]
        total_province = 0
        total_main = 0
        
        for month in range(1, 7):
            if month in monthly_data and town in monthly_data[month]:
                province_count = monthly_data[month][town]['省平台']
                main_count = monthly_data[month][town]['主站']
            else:
                province_count = 0
                main_count = 0
            
            row.extend([province_count, main_count])
            total_province += province_count
            total_main += main_count
        
        row.extend([total_province, total_main])
        summary_data.append(row)
    
    # 创建DataFrame并保存
    summary_df = pd.DataFrame(summary_data, columns=columns)
    
    # 保存到Excel文件
    output_file = "2025年1-6月学习强国签发稿件汇总表_重新排序.xlsx"
    summary_df.to_excel(output_file, index=False, engine='openpyxl')
    
    print(f"\n汇总表已保存到: {output_file}")
    print("\n汇总表内容:")
    print(summary_df.to_string(index=False))
    
    # 创建每月单独的汇总表
    for month in range(1, 7):
        if month in monthly_data:
            month_columns = ['乡镇街道', f'{month}月省平台', f'{month}月主站']
            month_data = []

            # 按指定顺序处理乡镇街道
            for town in town_order:
                if town not in all_towns:
                    continue
                if town in monthly_data[month]:
                    province_count = monthly_data[month][town]['省平台']
                    main_count = monthly_data[month][town]['主站']
                else:
                    province_count = 0
                    main_count = 0
                month_data.append([town, province_count, main_count])
            
            month_df = pd.DataFrame(month_data, columns=month_columns)
            month_file = f"2025年{month}月学习强国签发稿件统计表_重新排序.xlsx"
            month_df.to_excel(month_file, index=False, engine='openpyxl')
            print(f"{month}月统计表已保存到: {month_file}")

if __name__ == "__main__":
    main()
