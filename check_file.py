import pandas as pd
import os

# 获取文件列表
files = [f for f in os.listdir('.') if f.endswith('.xls') and '2025.1' in f]
if files:
    file = files[0]
    print(f"检查文件: {file}")
    
    try:
        df = pd.read_excel(file, engine='xlrd')
        print(f"数据框形状: {df.shape}")
        print(f"列名: {df.columns.tolist()}")
        print("\n前15行数据:")
        for i in range(min(15, len(df))):
            row = df.iloc[i]
            print(f"行{i}: {[str(x) for x in row.values]}")
    except Exception as e:
        print(f"错误: {e}")
